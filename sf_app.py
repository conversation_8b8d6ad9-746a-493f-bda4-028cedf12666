from flask import Flask, request, jsonify, render_template, redirect, url_for, flash
import pandas as pd
import joblib
import json
from io import BytesIO
import os
from werkzeug.utils import secure_filename

# Import directly from the sales_forecast package
import sys
sys.path.append('.')  # Add current directory to path
from sales_forecast.pipeline.data_preparation import clean_data, data_identifying
from sales_forecast.pipeline.model_training import model_training
from sales_forecast.pipeline.forecast import predict_future

app = Flask(__name__,
            template_folder='sales_forecast/templates',
            static_folder='sales_forecast/static')
app.secret_key = 'sales_forecast_secret_key'

# Create directories for models if it doesn't exist
os.makedirs('models', exist_ok=True)

# Global variables to store model information
best_model = None
best_model_name = None
feature_columns = None
target_column = None
date_column = None
data_frequency = None
seasonal_periods = None
historical_data = None

# Load model if it exists
if os.path.exists('models/best_model.pkl'):
    try:
        best_model = joblib.load('models/best_model.pkl')

        with open('models/best_model_name.json', 'r') as f:
            best_model_name = json.load(f)['best_model_name']

        with open('models/config.json', 'r') as f:
            config = json.load(f)
            feature_columns = config['feature_columns']
            target_column = config['target_column']
            date_column = config['date_column']
            data_frequency = config.get('data_frequency', 'monthly')
            seasonal_periods = config.get('seasonal_periods', 12)

        # Load historical data if it exists
        if os.path.exists('models/historical_data.csv'):
            historical_data = pd.read_csv('models/historical_data.csv')
            historical_data[date_column] = pd.to_datetime(historical_data[date_column])

    except Exception as e:
        print(f"Error loading model: {e}")

@app.route('/')
def home():
    return render_template('index.html', model_name=best_model_name)

@app.route('/documentation')
def documentation():
    return render_template('documentation.html')

@app.route('/upload', methods=['GET', 'POST'])
def upload_dataset():
    global best_model, best_model_name, feature_columns, target_column, date_column, data_frequency, seasonal_periods, historical_data

    if request.method == 'GET':
        return render_template('upload.html')

    if 'file' not in request.files:
        flash('No file part. Please select a file to upload.', 'warning')
        return redirect(request.url)

    file = request.files['file']

    if file.filename == '':
        flash('No selected file. Please choose a CSV file to upload.', 'warning')
        return redirect(request.url)

    if file and file.filename.endswith('.csv'):
        try:
            data = pd.read_csv(file)

            if data.shape[0] < 10:
                flash('Dataset too small to train a model. Minimum 10 rows required.', 'danger')
                return redirect(request.url)

            data, y = clean_data(data)
            date_column, feature_columns, target_column, data_frequency, seasonal_periods, freq_code = data_identifying(data, y)

            # Store historical data for visualization
            historical_data = data.copy()

            # Prepare training and test data - handle case when no features
            if feature_columns and len(feature_columns) > 0:
                columns_to_use = [date_column] + feature_columns + [target_column]
            else:
                columns_to_use = [date_column, target_column]

            train = data[columns_to_use][:-3]
            test = data[columns_to_use][-3:]

            model_results = model_training(train, test, data, date_column, target_column, feature_columns,
                                         data_frequency, seasonal_periods)
            best_model = model_results[0]
            best_model_name = model_results[7]

            joblib.dump(best_model, 'models/best_model.pkl')

            with open('models/best_model_name.json', 'w') as f:
                json.dump({'best_model_name': best_model_name}, f)

            with open('models/config.json', 'w') as f:
                json.dump({
                    'feature_columns': feature_columns,
                    'target_column': target_column,
                    'date_column': date_column,
                    'data_frequency': data_frequency,
                    'seasonal_periods': seasonal_periods
                }, f)

            # Save historical data for visualization
            historical_data.to_csv('models/historical_data.csv', index=False)

            flash(f'Model trained successfully! Best model: {best_model_name} (Data frequency: {data_frequency})', 'success')
            return redirect(url_for('predict'))

        except Exception as e:
            flash(f'Error during training: {str(e)}', 'danger')
            return redirect(request.url)
    else:
        flash('Please upload a CSV file. Only CSV files are supported.', 'warning')
        return redirect(request.url)

@app.route('/predict', methods=['GET', 'POST'])
def predict():
    global best_model, best_model_name, feature_columns, date_column, data_frequency, historical_data

    if best_model is None:
        flash('No model trained yet. Please upload and train a dataset first.', 'info')
        return redirect(url_for('upload_dataset'))

    if request.method == 'GET':
        return render_template('predict.html',
                              feature_columns=feature_columns,
                              date_column=date_column,
                              model_name=best_model_name,
                              data_frequency=data_frequency)

    try:
        input_data = request.form.to_dict()
        print(f"Raw form data received: {input_data}")

        # Convert numeric values from strings
        for key in input_data:
            if key != 'ds':  # Skip date column
                try:
                    input_data[key] = float(input_data[key])
                except ValueError:
                    print(f"Could not convert {key}={input_data[key]} to float")
                    pass

        print(f"Processed input data: {input_data}")
        df = pd.DataFrame([input_data])

        if 'ds' not in df.columns:
            flash('Input data must contain date column (ds).', 'warning')
            return redirect(request.url)

        df['ds'] = pd.to_datetime(df['ds'])

        # Print debug information
        print(f"Input data for prediction: {df}")
        print(f"Feature columns expected: {feature_columns}")

        # No need to clean data for prediction
        # df_cleaned, _ = clean_data(df)

        # Check which feature columns are available
        if feature_columns and len(feature_columns) > 0:
            available_features = [col for col in feature_columns if col in df.columns]
            missing_cols = set(feature_columns) - set(df.columns)

            if missing_cols:
                print(f'Warning: Missing feature columns: {missing_cols}')
                print('Proceeding with available features or ds-only prediction')
                flash(f'Note: Missing feature columns {missing_cols}. Using available data for prediction.', 'info')
        else:
            # No features expected - this is a ds-only model
            available_features = []
            missing_cols = set()
            print('Model was trained without features - using ds-only prediction')
            flash('Using time-series prediction (no features required).', 'info')

        # Prepare data for prediction - include ds and any available features
        prediction_data = df[['ds']].copy()
        for col in available_features:
            if col in df.columns:
                prediction_data[col] = df[col]

        # Make prediction
        print(f"Making prediction with data: {prediction_data}")
        print(f"Using model: {best_model_name}")
        prediction = predict_future(best_model, best_model_name, prediction_data)
        print(f"Prediction result: {prediction}")
        print(f"Prediction type: {type(prediction)}")
        print(f"Prediction length: {len(prediction) if hasattr(prediction, '__len__') else 'N/A'}")

        # Check if prediction is valid
        if prediction is None:
            raise ValueError("Prediction returned None")

        # Handle different prediction types (numpy array, pandas Series, list)
        if hasattr(prediction, '__len__') and len(prediction) == 0:
            raise ValueError("Prediction returned empty result")

        # Extract the first prediction value
        if hasattr(prediction, '__getitem__'):
            pred_value = prediction[0]
        elif hasattr(prediction, 'iloc'):
            pred_value = prediction.iloc[0]
        else:
            pred_value = prediction

        # Convert to float safely
        try:
            pred_float = float(pred_value)
        except (ValueError, TypeError) as e:
            print(f"Error converting prediction to float: {e}")
            print(f"Prediction value: {pred_value}, type: {type(pred_value)}")
            raise ValueError(f"Could not convert prediction to float: {pred_value}")

        if pred_float == 0:
            print("Warning: Prediction returned 0 - this might indicate an issue with the model")

        # Format result
        result = {
            "prediction_date": df['ds'].iloc[0].date().isoformat(),
            "predicted_sales": pred_float
        }

        # Store the user prediction and generate chart data with user predictions
        chart_data = None
        if historical_data is not None:
            try:
                from sales_forecast.pipeline.visualization import (
                    store_user_prediction,
                    get_user_predictions,
                    prepare_user_predictions_chart_data
                )

                # Store this prediction
                prediction_date_str = df['ds'].iloc[0].strftime('%Y-%m-%d')
                store_user_prediction(
                    prediction_date=prediction_date_str,
                    prediction_value=pred_float,
                    model_name=best_model_name
                )

                # Get all user predictions for this model
                user_predictions = get_user_predictions(model_name=best_model_name)

                # Generate chart data showing historical data + user predictions
                chart_data = prepare_user_predictions_chart_data(
                    historical_data=historical_data,
                    user_predictions_list=user_predictions,
                    date_column=date_column,
                    target_column=target_column,
                    model_name=best_model_name
                )
            except Exception as e:
                print(f"Error generating chart data: {e}")
                chart_data = None

        return render_template('result.html',
                              result=result,
                              model_name=best_model_name,
                              chart_data=json.dumps(chart_data) if chart_data else None,
                              data_frequency=data_frequency)

    except Exception as e:
        flash(f'Error during prediction: {str(e)}', 'danger')
        return redirect(request.url)

# API endpoints for programmatic access
@app.route('/api/upload', methods=['POST'])
def api_upload_dataset():
    global best_model, best_model_name, feature_columns, target_column, date_column

    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if file and file.filename.endswith('.csv'):
        try:
            data = pd.read_csv(file)

            if data.shape[0] < 10:
                return jsonify({"error": "Dataset too small to train a model. Minimum 10 rows required."}), 400

            data, y = clean_data(data)
            date_column, feature_columns, target_column = data_identifying(data, y)

            # Prepare training and test data - handle case when no features
            if feature_columns and len(feature_columns) > 0:
                columns_to_use = [date_column] + feature_columns + [target_column]
            else:
                columns_to_use = [date_column, target_column]

            train = data[columns_to_use][:-3]
            test = data[columns_to_use][-3:]

            model_results = model_training(train, test, data, date_column, target_column, feature_columns)
            best_model = model_results[0]
            best_model_name = model_results[7]

            joblib.dump(best_model, 'models/best_model.pkl')

            with open('models/best_model_name.json', 'w') as f:
                json.dump({'best_model_name': best_model_name}, f)

            with open('models/config.json', 'w') as f:
                json.dump({
                    'feature_columns': feature_columns,
                    'target_column': target_column,
                    'date_column': date_column
                }, f)

            return jsonify({"message": f"Model trained successfully. Best model: {best_model_name}"}), 200

        except Exception as e:
            return jsonify({"error": f"Error during training: {str(e)}"}), 500
    else:
        return jsonify({"error": "Please upload a CSV file"}), 400

@app.route('/api/predict', methods=['POST'])
def api_predict():
    global best_model, best_model_name, feature_columns, date_column

    if best_model is None:
        return jsonify({"error": "No model trained yet. Please upload and train a dataset first."}), 400

    try:
        input_data = request.json

        df = pd.DataFrame([input_data])

        if 'ds' not in df.columns:
            return jsonify({"error": "Input data must contain 'ds' (date) column."}), 400

        df['ds'] = pd.to_datetime(df['ds'])

        # Print debug information
        print(f"API Input data for prediction: {df}")
        print(f"Feature columns expected: {feature_columns}")

        # Check which feature columns are available
        if feature_columns and len(feature_columns) > 0:
            available_features = [col for col in feature_columns if col in df.columns]
            missing_cols = set(feature_columns) - set(df.columns)

            if missing_cols:
                print(f'API Warning: Missing feature columns: {missing_cols}')
                print('API: Proceeding with available features or ds-only prediction')
        else:
            # No features expected - this is a ds-only model
            available_features = []
            missing_cols = set()
            print('API: Model was trained without features - using ds-only prediction')

        # Prepare data for prediction - include ds and any available features
        prediction_data = df[['ds']].copy()
        for col in available_features:
            if col in df.columns:
                prediction_data[col] = df[col]

        # Make prediction
        prediction = predict_future(best_model, best_model_name, prediction_data)

        # Handle different prediction types safely
        if prediction is None:
            raise ValueError("Prediction returned None")

        # Extract the first prediction value
        if hasattr(prediction, '__getitem__'):
            pred_value = prediction[0]
        elif hasattr(prediction, 'iloc'):
            pred_value = prediction.iloc[0]
        else:
            pred_value = prediction

        # Convert to float safely
        pred_float = float(pred_value)

        return jsonify({
             "prediction_date": df['ds'].iloc[0].date().isoformat(),
            "predicted_sales": pred_float
        }), 200

    except Exception as e:
        return jsonify({"error": f"Error during prediction: {str(e)}"}), 500

@app.route('/predictions-history')
def predictions_history():
    """View all user predictions with visualization"""
    global best_model_name, historical_data, date_column, target_column

    if historical_data is None:
        flash('No historical data available. Please upload and train a dataset first.', 'info')
        return redirect(url_for('upload_dataset'))

    try:
        from sales_forecast.pipeline.visualization import (
            get_user_predictions,
            prepare_user_predictions_chart_data
        )

        # Get all user predictions
        user_predictions = get_user_predictions(model_name=best_model_name)

        chart_data = None
        if user_predictions:
            # Generate chart data showing historical data + all user predictions
            chart_data = prepare_user_predictions_chart_data(
                historical_data=historical_data,
                user_predictions_list=user_predictions,
                date_column=date_column,
                target_column=target_column,
                model_name=best_model_name
            )

        return render_template('predictions_history.html',
                              predictions=user_predictions,
                              model_name=best_model_name,
                              chart_data=json.dumps(chart_data) if chart_data else None)

    except Exception as e:
        flash(f'Error loading predictions history: {str(e)}', 'danger')
        return redirect(url_for('home'))

@app.route('/clear-predictions', methods=['POST'])
def clear_predictions():
    """Clear all stored user predictions"""
    try:
        from sales_forecast.pipeline.visualization import clear_user_predictions
        clear_user_predictions()
        flash('All predictions cleared successfully!', 'success')
    except Exception as e:
        flash(f'Error clearing predictions: {str(e)}', 'danger')

    return redirect(url_for('predictions_history'))

if __name__ == '__main__':
    print("Starting Flask application...")
    app.run(host='0.0.0.0', port=8000,debug=True)
