# Enhanced Sales Forecasting Features

## Overview

The sales forecasting application has been enhanced to handle both daily and monthly datasets with improved visualization capabilities. The system now automatically detects data frequency and adapts model parameters accordingly.

## New Features

### 1. Automatic Data Frequency Detection

The system now automatically detects whether your data is:
- **Daily**: Data points every day (seasonal_periods: 365)
- **Weekly**: Data points every week (seasonal_periods: 52)
- **Monthly**: Data points every month (seasonal_periods: 12)
- **Quarterly**: Data points every quarter (seasonal_periods: 4)
- **Yearly**: Data points every year (seasonal_periods: 1)

### 2. Frequency-Aware Model Training

Models are now trained with appropriate seasonal parameters based on detected frequency:

#### Exponential Smoothing
- **Daily**: Tests seasonal patterns of 7, 30, and 365 days
- **Weekly**: Tests seasonal patterns of 4, 13, and 52 weeks
- **Monthly**: Tests seasonal patterns of 3, 6, and 12 months

#### SARIMA
- **Daily**: Uses weekly seasonality (7 days)
- **Weekly**: Uses yearly seasonality (52 weeks)
- **Monthly**: Uses yearly seasonality (12 months)

#### Prophet
- **Daily**: Adds weekly and monthly seasonality components
- **Weekly**: Adds monthly and quarterly seasonality components
- **Monthly**: Uses default yearly seasonality

### 3. Interactive Forecast Visualization

The application now generates interactive charts showing:
- **Historical sales data** (blue line)
- **Future predictions** (red dashed line)
- **Trend analysis** with past and predicted points in separate tiles

### 4. Enhanced User Interface

- Displays detected data frequency in the prediction interface
- Shows forecast visualization alongside prediction results
- Provides better feedback about model capabilities

## How to Use

### 1. Upload Dataset

Upload a CSV file with:
- **Required columns**: `ds` (date) and `y` (target sales value)
- **Optional columns**: Any numerical features (e.g., MRP, Discount, Marketing_Spend)

The system will automatically:
- Detect the data frequency
- Clean and prepare the data
- Train models with appropriate parameters
- Save configuration for future predictions

### 2. Make Predictions

After training, you can:
- Enter a future date for prediction
- Provide feature values (if your model uses features)
- View both numerical results and visual forecasts

### 3. View Results

The results page shows:
- **Prediction details**: Date and predicted sales value
- **Model information**: Which model was used and data frequency
- **Interactive chart**: Historical trends and future predictions
- **Visual insights**: Clear separation between historical and predicted data

## Technical Implementation

### Data Preparation (`data_preparation.py`)
- `detect_data_frequency()`: Analyzes date differences to determine frequency
- Enhanced `data_identifying()`: Returns frequency information
- Improved `clean_data()`: Better logging and validation

### Model Training (`model_training.py`)
- Updated `model_training()`: Accepts frequency parameters
- Adaptive seasonal parameters based on data frequency
- Frequency-specific Prophet seasonality configuration

### Visualization (`visualization.py`)
- `prepare_chart_data()`: Generates Chart.js compatible data
- `generate_forecast_chart_data()`: Creates forecast visualizations
- `create_summary_stats()`: Provides statistical insights

### Flask Application (`sf_app.py`)
- Enhanced global variables for frequency tracking
- Updated routes to handle visualization
- Improved model persistence with frequency information

## Supported Data Formats

### Daily Data Example
```csv
ds,y
2023-01-01,100
2023-01-02,105
2023-01-03,98
...
```

### Monthly Data Example
```csv
ds,y,MRP,Discount
2023-01-01,1000,250,15
2023-02-01,1100,260,20
2023-03-01,950,240,10
...
```

### Weekly Data Example
```csv
ds,y
2023-01-02,500
2023-01-09,520
2023-01-16,480
...
```

## Benefits

1. **Automatic Adaptation**: No manual configuration needed for different data frequencies
2. **Better Accuracy**: Models use appropriate seasonal parameters for each frequency
3. **Visual Insights**: Interactive charts help understand trends and predictions
4. **Flexible Input**: Works with minimal datasets (ds + y only) or feature-rich datasets
5. **User-Friendly**: Clear feedback about data frequency and model capabilities

## API Usage

The enhanced features are also available through the API endpoints:

### Upload Dataset
```bash
POST /api/upload
Content-Type: multipart/form-data
```

### Make Prediction
```bash
POST /api/predict
Content-Type: application/json

{
    "ds": "2024-01-01",
    "feature1": value1,
    "feature2": value2
}
```

## Testing

Run the test script to verify all features:
```bash
python test_enhanced_features.py
```

This will test:
- Frequency detection for different data types
- Enhanced data preparation
- Visualization generation
- Model training setup

## Troubleshooting

### Common Issues

1. **Chart not displaying**: Ensure Chart.js is loaded and chart_data is valid JSON
2. **Frequency detection errors**: Check that date column is properly formatted
3. **Model training failures**: Verify sufficient data points for seasonal models

### Debug Information

The application provides detailed logging for:
- Data frequency detection results
- Model training progress
- Prediction generation steps
- Visualization creation process

## Future Enhancements

Potential improvements include:
- Confidence intervals in visualizations
- Multiple forecast scenarios
- Advanced seasonality detection
- Real-time data streaming support
- Export functionality for charts and predictions
