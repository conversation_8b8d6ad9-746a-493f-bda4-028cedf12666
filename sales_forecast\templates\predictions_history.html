{% extends "base.html" %}

{% block title %}Predictions History{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <div class="text-center mb-4">
                <h2 class="display-6">Your Predictions History</h2>
                <p class="lead">View all your calculated predictions with historical trends</p>
            </div>

            {% if predictions %}
                <!-- Chart Visualization -->
                {% if chart_data %}
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Historical Sales & Your Predictions - {{ model_name }}</h5>
                    </div>
                    <div class="card-body">
                        <div style="height: 500px;">
                            <canvas id="predictionsChart"></canvas>
                        </div>
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>Chart Legend:</strong><br>
                                        • Blue line: Historical sales data<br>
                                        • Colored points: Your predictions<br>
                                        • Each prediction has a unique color
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb"></i>
                                        <strong>Understanding the Chart:</strong><br>
                                        • Historical trend shows past sales patterns<br>
                                        • Your predictions appear as individual points<br>
                                        • Multiple predictions for same date will overlap<br>
                                        • Hover over points for detailed values
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Predictions Table -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Predictions List ({{ predictions|length }} total)</h5>
                        <form method="POST" action="{{ url_for('clear_predictions') }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger btn-sm" 
                                    onclick="return confirm('Are you sure you want to clear all predictions?')">
                                <i class="bi bi-trash"></i> Clear All
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Prediction Date</th>
                                        <th>Predicted Sales</th>
                                        <th>Model Used</th>
                                        <th>When Made</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prediction in predictions %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ prediction.date }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ prediction.prediction|round(2) }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ prediction.model_name }}</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ prediction.timestamp[:19]|replace('T', ' ') }}
                                            </small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Predictions Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ predictions|length }}</h4>
                                    <small class="text-muted">Total Predictions</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ (predictions|map(attribute='prediction')|max)|round(2) }}</h4>
                                    <small class="text-muted">Highest Prediction</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ (predictions|map(attribute='prediction')|min)|round(2) }}</h4>
                                    <small class="text-muted">Lowest Prediction</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ ((predictions|map(attribute='prediction')|sum) / (predictions|length))|round(2) }}</h4>
                                    <small class="text-muted">Average Prediction</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            {% else %}
                <!-- No Predictions Message -->
                <div class="card">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up display-1 text-muted mb-3"></i>
                        <h4>No Predictions Yet</h4>
                        <p class="text-muted">You haven't made any predictions yet. Start by making your first prediction!</p>
                        <a href="{{ url_for('predict') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Make Your First Prediction
                        </a>
                    </div>
                </div>
            {% endif %}

            <!-- Navigation -->
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('predict') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Make New Prediction
                </a>
                <a href="{{ url_for('home') }}" class="btn btn-secondary">
                    <i class="bi bi-house"></i> Back to Home
                </a>
            </div>
        </div>
    </div>
</div>

{% if chart_data %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chartData = {{ chart_data|safe }};

    if (chartData) {
        const ctx = document.getElementById('predictionsChart').getContext('2d');

        // Enhanced chart configuration
        chartData.options = {
            ...chartData.options,
            plugins: {
                ...chartData.options.plugins,
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return 'Date: ' + context[0].label;
                        },
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += new Intl.NumberFormat('en-US', {
                                    style: 'decimal',
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }).format(context.parsed.y);
                            }
                            return label;
                        }
                    }
                }
            }
        };

        new Chart(ctx, chartData);
    }
});
</script>
{% endif %}
{% endblock %}
