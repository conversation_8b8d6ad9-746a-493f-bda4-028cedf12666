import pandas as pd
import numpy as np

def detect_data_frequency(data, date_column='ds'):
    """
    Detect the frequency of the time series data (daily, weekly, monthly)
    Returns frequency info and appropriate seasonal parameters
    """
    if date_column not in data.columns:
        return 'unknown', 12, 'M'

    # Sort by date to ensure proper frequency detection
    data_sorted = data.sort_values(date_column)
    dates = pd.to_datetime(data_sorted[date_column])

    # Calculate differences between consecutive dates
    date_diffs = dates.diff().dropna()

    # Get the most common difference
    most_common_diff = date_diffs.mode()

    if len(most_common_diff) == 0:
        return 'unknown', 12, 'M'

    avg_diff = most_common_diff.iloc[0]

    # Determine frequency based on average difference
    if avg_diff <= pd.Timedelta(days=1):
        frequency = 'daily'
        seasonal_periods = 365  # Daily seasonality (yearly)
        freq_code = 'D'
    elif avg_diff <= pd.Timedelta(days=7):
        frequency = 'weekly'
        seasonal_periods = 52   # Weekly seasonality (yearly)
        freq_code = 'W'
    elif avg_diff <= pd.Timedelta(days=31):
        frequency = 'monthly'
        seasonal_periods = 12   # Monthly seasonality (yearly)
        freq_code = 'M'
    elif avg_diff <= pd.Timedelta(days=92):
        frequency = 'quarterly'
        seasonal_periods = 4    # Quarterly seasonality
        freq_code = 'Q'
    else:
        frequency = 'yearly'
        seasonal_periods = 1    # No seasonality for yearly data
        freq_code = 'Y'

    print(f"Detected data frequency: {frequency} (seasonal_periods: {seasonal_periods})")
    return frequency, seasonal_periods, freq_code

def clean_data(data):
    """
    Clean and prepare the data for analysis
    """
    # Print data info for debugging
    print("Data shape:", data.shape)
    print("Data columns:", list(data.columns))

    # Convert date column to datetime
    if 'ds' in data.columns:
        data['ds'] = pd.to_datetime(data['ds'])
        print("Date column 'ds' converted to datetime")
        print("Date range:", data['ds'].min(), "to", data['ds'].max())

    # Get target column name if it exists
    if 'y' in data.columns:
        y = data['y'].name
        print(f"Target column '{y}' found")
    else:
        # For prediction data, there might not be a target column
        y = None
        print("No target column 'y' found (prediction mode)")

    return data, y

def data_identifying(data, y=None):
    """
    Identify columns and detect data frequency
    """
    data.columns = data.columns.str.strip()

    # Identify the date column
    date_columns = data.select_dtypes(include=['datetime', 'datetime64']).columns
    if len(date_columns) > 0:
        date_column = date_columns[0]
    else:
        # If no datetime column is found, assume 'ds' is the date column
        date_column = 'ds'

    # Detect data frequency
    frequency, seasonal_periods, freq_code = detect_data_frequency(data, date_column)

    # Identify the numerical feature columns
    feature_columns = data.select_dtypes(include=['float64', 'int64']).columns.tolist()

    # Check for non-numeric columns
    characteristic_columns = data.select_dtypes(include=['object']).columns.tolist()
    if characteristic_columns and characteristic_columns != [date_column]:
        print(f'Warning: {characteristic_columns} will not be considered as they are not numerical')

    # Identify the target column
    target_column = y

    # Remove the target column from the feature list if present
    if target_column in feature_columns:
        feature_columns.remove(target_column)

    # For prediction, we don't need to return the target column
    if target_column is None and len(feature_columns) > 0:
        print(f"Using features: {feature_columns}")

    print(f"Date column: {date_column}")
    print(f"Feature columns: {feature_columns}")
    print(f"Target column: {target_column}")
    print(f"Data frequency: {frequency} (seasonal_periods: {seasonal_periods})")

    return date_column, feature_columns, target_column, frequency, seasonal_periods, freq_code