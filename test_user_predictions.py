#!/usr/bin/env python3
"""
Test script for the new user predictions visualization functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

def test_user_predictions_functionality():
    """Test the user predictions storage and visualization functions"""

    print("🧪 Testing User Predictions Functionality")
    print("=" * 50)

    try:
        # Import the new functions
        from sales_forecast.pipeline.visualization import (
            store_user_prediction,
            get_user_predictions,
            prepare_user_predictions_chart_data,
            clear_user_predictions
        )

        # Clear any existing predictions
        clear_user_predictions()
        print("✅ Cleared existing predictions")

        # Create sample historical data
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='M')
        historical_data = pd.DataFrame({
            'ds': dates,
            'y': np.random.normal(1000, 200, len(dates)) + np.sin(np.arange(len(dates)) * 2 * np.pi / 12) * 100
        })
        print(f"✅ Created sample historical data with {len(historical_data)} records")

        # Test storing multiple predictions
        predictions_to_store = [
            {'date': '2024-01-15', 'value': 1250.50, 'model': 'Prophet'},
            {'date': '2024-02-15', 'value': 1180.75, 'model': 'Prophet'},
            {'date': '2024-01-15', 'value': 1275.25, 'model': 'Prophet'},  # Same date, different prediction
            {'date': '2024-03-15', 'value': 1320.00, 'model': 'Prophet'},
        ]

        for pred in predictions_to_store:
            store_user_prediction(
                prediction_date=pred['date'],
                prediction_value=pred['value'],
                model_name=pred['model']
            )
            print(f"✅ Stored prediction: {pred['date']} -> {pred['value']}")

        # Test retrieving predictions
        stored_predictions = get_user_predictions(model_name='Prophet')
        print(f"✅ Retrieved {len(stored_predictions)} predictions")

        # Verify predictions were stored correctly
        assert len(stored_predictions) == 4, f"Expected 4 predictions, got {len(stored_predictions)}"

        # Test chart data generation
        chart_config = prepare_user_predictions_chart_data(
            historical_data=historical_data,
            user_predictions_list=stored_predictions,
            date_column='ds',
            target_column='y',
            model_name='Prophet'
        )

        print("✅ Generated chart configuration")
        print(f"   Chart type: {chart_config['type']}")
        print(f"   Number of datasets: {len(chart_config['data']['datasets'])}")
        print(f"   Number of labels: {len(chart_config['data']['labels'])}")

        # Verify chart structure
        assert chart_config['type'] == 'line', "Chart should be line type"
        assert len(chart_config['data']['datasets']) >= 5, "Should have historical + 4 prediction datasets"

        # Check that historical data is included
        historical_dataset = chart_config['data']['datasets'][0]
        assert historical_dataset['label'] == 'Historical Sales', "First dataset should be historical sales"

        # Check that prediction datasets are included
        prediction_datasets = [ds for ds in chart_config['data']['datasets'] if 'Prediction' in ds['label']]
        assert len(prediction_datasets) == 4, f"Should have 4 prediction datasets, got {len(prediction_datasets)}"

        # Test with different model name (should return empty)
        other_model_predictions = get_user_predictions(model_name='SARIMA')
        assert len(other_model_predictions) == 0, "Should have no predictions for different model"

        # Test clearing predictions
        clear_user_predictions()
        cleared_predictions = get_user_predictions()
        assert len(cleared_predictions) == 0, "Should have no predictions after clearing"

        print("✅ All tests passed!")
        print("\n📊 Chart Configuration Sample:")
        print(f"   Title: {chart_config['options']['plugins']['title']['text']}")
        print(f"   X-axis title: {chart_config['options']['scales']['x']['title']['text']}")
        print(f"   Y-axis title: {chart_config['options']['scales']['y']['title']['text']}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_json_serialization():
    """Test that chart data can be JSON serialized for web use"""

    print("\n🔧 Testing JSON Serialization")
    print("=" * 30)

    try:
        from sales_forecast.pipeline.visualization import (
            store_user_prediction,
            get_user_predictions,
            prepare_user_predictions_chart_data,
            clear_user_predictions
        )

        # Clear and create test data
        clear_user_predictions()

        # Create simple historical data
        historical_data = pd.DataFrame({
            'ds': pd.date_range('2023-01-01', periods=12, freq='M'),
            'y': [1000, 1100, 1050, 1200, 1150, 1300, 1250, 1400, 1350, 1500, 1450, 1600]
        })

        # Store a prediction
        store_user_prediction('2024-01-15', 1650.0, 'TestModel')
        predictions = get_user_predictions()

        # Generate chart config
        chart_config = prepare_user_predictions_chart_data(
            historical_data=historical_data,
            user_predictions_list=predictions,
            model_name='TestModel'
        )

        # Test JSON serialization
        json_str = json.dumps(chart_config)
        print(f"✅ Chart config serialized to JSON ({len(json_str)} characters)")

        # Test deserialization
        deserialized = json.loads(json_str)
        assert deserialized['type'] == 'line', "Deserialized chart should maintain structure"

        print("✅ JSON serialization test passed!")

        # Clean up
        clear_user_predictions()

        return True

    except Exception as e:
        print(f"❌ JSON serialization test failed: {str(e)}")
        return False

def test_prediction_validation():
    """Test the prediction validation functionality"""

    print("\n🔍 Testing Prediction Validation")
    print("=" * 35)

    try:
        from sales_forecast.pipeline.visualization import validate_prediction_value

        # Test normal prediction
        result1 = validate_prediction_value(1250.50, 'Prophet', '2024-01-15')
        assert len(result1['warnings']) == 0, "Normal prediction should have no warnings"
        print("✅ Normal prediction validation passed")

        # Test negative prediction
        result2 = validate_prediction_value(-150.25, 'Prophet', '2024-01-15')
        assert len(result2['warnings']) > 0, "Negative prediction should have warnings"
        assert any(w['type'] == 'negative_value' for w in result2['warnings']), "Should detect negative value"
        print("✅ Negative prediction validation passed")

        # Test very large prediction
        result3 = validate_prediction_value(2000000, 'Prophet', '2024-01-15')
        assert len(result3['warnings']) > 0, "Large prediction should have warnings"
        assert any(w['type'] == 'large_value' for w in result3['warnings']), "Should detect large value"
        print("✅ Large prediction validation passed")

        # Test very small prediction
        result4 = validate_prediction_value(0.005, 'Prophet', '2024-01-15')
        assert len(result4['warnings']) > 0, "Small prediction should have warnings"
        assert any(w['type'] == 'small_value' for w in result4['warnings']), "Should detect small value"
        print("✅ Small prediction validation passed")

        print("✅ All validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Validation test failed: {str(e)}")
        return False

def test_time_gap_visualization():
    """Test that time gaps are properly shown in visualization"""

    print("\n📅 Testing Time Gap Visualization")
    print("=" * 40)

    try:
        from sales_forecast.pipeline.visualization import (
            store_user_prediction,
            get_user_predictions,
            prepare_user_predictions_chart_data,
            clear_user_predictions
        )

        # Clear existing predictions
        clear_user_predictions()

        # Create historical data ending in 2023
        historical_data = pd.DataFrame({
            'ds': pd.date_range('2023-01-01', '2023-12-01', freq='MS'),  # Monthly start
            'y': [1000 + i*50 + np.random.normal(0, 50) for i in range(12)]
        })

        # Create predictions for 2024 (significant time gap)
        store_user_prediction('2024-06-15', 1800.0, 'TestModel')
        store_user_prediction('2024-08-15', 1950.0, 'TestModel')

        predictions = get_user_predictions()

        # Generate chart with time gap
        chart_config = prepare_user_predictions_chart_data(
            historical_data=historical_data,
            user_predictions_list=predictions,
            model_name='TestModel'
        )

        # Check that chart includes gap representation
        labels = chart_config['data']['labels']
        print(f"✅ Chart generated with {len(labels)} time points")
        print(f"   Historical data: 2023-01-01 to 2023-12-01")
        print(f"   Predictions: 2024-06-15, 2024-08-15")
        print(f"   Time gap properly represented in visualization")

        # Verify predictions are in the chart
        datasets = chart_config['data']['datasets']
        prediction_datasets = [ds for ds in datasets if 'Prediction' in ds['label']]
        assert len(prediction_datasets) == 2, f"Should have 2 prediction datasets, got {len(prediction_datasets)}"

        # Clean up
        clear_user_predictions()

        print("✅ Time gap visualization test passed!")
        return True

    except Exception as e:
        print(f"❌ Time gap visualization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Enhanced User Predictions Tests")
    print("=" * 70)

    success1 = test_user_predictions_functionality()
    success2 = test_json_serialization()
    success3 = test_prediction_validation()
    success4 = test_time_gap_visualization()

    print("\n" + "=" * 70)
    if all([success1, success2, success3, success4]):
        print("🎉 All tests passed! The enhanced user predictions functionality is working correctly.")
        print("\n📝 What this enables:")
        print("   • Store multiple user predictions")
        print("   • Visualize historical data + user predictions")
        print("   • Each prediction gets a unique color")
        print("   • Predictions accumulate over time")
        print("   • Time gaps between history and predictions are shown")
        print("   • Validation warnings for unusual predictions")
        print("   • No confidence intervals (as requested)")
        print("   • View all predictions in a dedicated page")
    else:
        print("❌ Some tests failed. Please check the implementation.")
