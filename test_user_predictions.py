#!/usr/bin/env python3
"""
Test script for the new user predictions visualization functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

def test_user_predictions_functionality():
    """Test the user predictions storage and visualization functions"""
    
    print("🧪 Testing User Predictions Functionality")
    print("=" * 50)
    
    try:
        # Import the new functions
        from sales_forecast.pipeline.visualization import (
            store_user_prediction,
            get_user_predictions,
            prepare_user_predictions_chart_data,
            clear_user_predictions
        )
        
        # Clear any existing predictions
        clear_user_predictions()
        print("✅ Cleared existing predictions")
        
        # Create sample historical data
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='M')
        historical_data = pd.DataFrame({
            'ds': dates,
            'y': np.random.normal(1000, 200, len(dates)) + np.sin(np.arange(len(dates)) * 2 * np.pi / 12) * 100
        })
        print(f"✅ Created sample historical data with {len(historical_data)} records")
        
        # Test storing multiple predictions
        predictions_to_store = [
            {'date': '2024-01-15', 'value': 1250.50, 'model': 'Prophet'},
            {'date': '2024-02-15', 'value': 1180.75, 'model': 'Prophet'},
            {'date': '2024-01-15', 'value': 1275.25, 'model': 'Prophet'},  # Same date, different prediction
            {'date': '2024-03-15', 'value': 1320.00, 'model': 'Prophet'},
        ]
        
        for pred in predictions_to_store:
            store_user_prediction(
                prediction_date=pred['date'],
                prediction_value=pred['value'],
                model_name=pred['model']
            )
            print(f"✅ Stored prediction: {pred['date']} -> {pred['value']}")
        
        # Test retrieving predictions
        stored_predictions = get_user_predictions(model_name='Prophet')
        print(f"✅ Retrieved {len(stored_predictions)} predictions")
        
        # Verify predictions were stored correctly
        assert len(stored_predictions) == 4, f"Expected 4 predictions, got {len(stored_predictions)}"
        
        # Test chart data generation
        chart_config = prepare_user_predictions_chart_data(
            historical_data=historical_data,
            user_predictions_list=stored_predictions,
            date_column='ds',
            target_column='y',
            model_name='Prophet'
        )
        
        print("✅ Generated chart configuration")
        print(f"   Chart type: {chart_config['type']}")
        print(f"   Number of datasets: {len(chart_config['data']['datasets'])}")
        print(f"   Number of labels: {len(chart_config['data']['labels'])}")
        
        # Verify chart structure
        assert chart_config['type'] == 'line', "Chart should be line type"
        assert len(chart_config['data']['datasets']) >= 5, "Should have historical + 4 prediction datasets"
        
        # Check that historical data is included
        historical_dataset = chart_config['data']['datasets'][0]
        assert historical_dataset['label'] == 'Historical Sales', "First dataset should be historical sales"
        
        # Check that prediction datasets are included
        prediction_datasets = [ds for ds in chart_config['data']['datasets'] if 'Prediction' in ds['label']]
        assert len(prediction_datasets) == 4, f"Should have 4 prediction datasets, got {len(prediction_datasets)}"
        
        # Test with different model name (should return empty)
        other_model_predictions = get_user_predictions(model_name='SARIMA')
        assert len(other_model_predictions) == 0, "Should have no predictions for different model"
        
        # Test clearing predictions
        clear_user_predictions()
        cleared_predictions = get_user_predictions()
        assert len(cleared_predictions) == 0, "Should have no predictions after clearing"
        
        print("✅ All tests passed!")
        print("\n📊 Chart Configuration Sample:")
        print(f"   Title: {chart_config['options']['plugins']['title']['text']}")
        print(f"   X-axis title: {chart_config['options']['scales']['x']['title']['text']}")
        print(f"   Y-axis title: {chart_config['options']['scales']['y']['title']['text']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_json_serialization():
    """Test that chart data can be JSON serialized for web use"""
    
    print("\n🔧 Testing JSON Serialization")
    print("=" * 30)
    
    try:
        from sales_forecast.pipeline.visualization import (
            store_user_prediction,
            get_user_predictions,
            prepare_user_predictions_chart_data,
            clear_user_predictions
        )
        
        # Clear and create test data
        clear_user_predictions()
        
        # Create simple historical data
        historical_data = pd.DataFrame({
            'ds': pd.date_range('2023-01-01', periods=12, freq='M'),
            'y': [1000, 1100, 1050, 1200, 1150, 1300, 1250, 1400, 1350, 1500, 1450, 1600]
        })
        
        # Store a prediction
        store_user_prediction('2024-01-15', 1650.0, 'TestModel')
        predictions = get_user_predictions()
        
        # Generate chart config
        chart_config = prepare_user_predictions_chart_data(
            historical_data=historical_data,
            user_predictions_list=predictions,
            model_name='TestModel'
        )
        
        # Test JSON serialization
        json_str = json.dumps(chart_config)
        print(f"✅ Chart config serialized to JSON ({len(json_str)} characters)")
        
        # Test deserialization
        deserialized = json.loads(json_str)
        assert deserialized['type'] == 'line', "Deserialized chart should maintain structure"
        
        print("✅ JSON serialization test passed!")
        
        # Clean up
        clear_user_predictions()
        
        return True
        
    except Exception as e:
        print(f"❌ JSON serialization test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting User Predictions Tests")
    print("=" * 60)
    
    success1 = test_user_predictions_functionality()
    success2 = test_json_serialization()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 All tests passed! The user predictions functionality is working correctly.")
        print("\n📝 What this enables:")
        print("   • Store multiple user predictions")
        print("   • Visualize historical data + user predictions")
        print("   • Each prediction gets a unique color")
        print("   • Predictions accumulate over time")
        print("   • View all predictions in a dedicated page")
    else:
        print("❌ Some tests failed. Please check the implementation.")
