#!/usr/bin/env python3
"""
Test script to verify the enhanced Flask application functionality
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """Create test datasets for different frequencies"""
    
    # Monthly test data
    monthly_dates = pd.date_range(start='2020-01-01', end='2023-12-01', freq='MS')
    monthly_data = pd.DataFrame({
        'ds': monthly_dates.strftime('%Y-%m-%d'),
        'y': 1000 + 50 * np.sin(np.arange(len(monthly_dates)) * 2 * np.pi / 12) + np.random.normal(0, 20, len(monthly_dates)),
        'MRP': np.random.uniform(200, 300, len(monthly_dates)),
        'Discount': np.random.uniform(10, 50, len(monthly_dates))
    })
    
    # Daily test data (last 3 months)
    daily_dates = pd.date_range(start='2023-10-01', end='2023-12-31', freq='D')
    daily_data = pd.DataFrame({
        'ds': daily_dates.strftime('%Y-%m-%d'),
        'y': 100 + 10 * np.sin(np.arange(len(daily_dates)) * 2 * np.pi / 7) + np.random.normal(0, 5, len(daily_dates))
    })
    
    return monthly_data, daily_data

def test_api_upload_and_predict():
    """Test the API upload and prediction functionality"""
    
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Enhanced API Functionality")
    print("=" * 50)
    
    # Create test data
    monthly_data, daily_data = create_test_data()
    
    # Save test data to CSV files
    monthly_data.to_csv('test_monthly_data.csv', index=False)
    daily_data.to_csv('test_daily_data.csv', index=False)
    
    # Test 1: Upload monthly data
    print("\n📤 Testing Monthly Data Upload...")
    try:
        with open('test_monthly_data.csv', 'rb') as f:
            files = {'file': ('test_monthly_data.csv', f, 'text/csv')}
            response = requests.post(f"{base_url}/api/upload", files=files, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Monthly data upload successful: {result['message']}")
            
            # Test prediction with features
            print("\n🔮 Testing Prediction with Features...")
            prediction_data = {
                "ds": "2024-01-01",
                "MRP": 250,
                "Discount": 20
            }
            
            pred_response = requests.post(f"{base_url}/api/predict", 
                                        json=prediction_data, 
                                        headers={'Content-Type': 'application/json'},
                                        timeout=30)
            
            if pred_response.status_code == 200:
                pred_result = pred_response.json()
                print(f"✅ Prediction successful: {pred_result['predicted_sales']:.2f} for {pred_result['prediction_date']}")
            else:
                print(f"❌ Prediction failed: {pred_response.text}")
                
        else:
            print(f"❌ Monthly data upload failed: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error during monthly data test: {e}")
        return False
    
    # Test 2: Upload daily data
    print("\n📤 Testing Daily Data Upload...")
    try:
        with open('test_daily_data.csv', 'rb') as f:
            files = {'file': ('test_daily_data.csv', f, 'text/csv')}
            response = requests.post(f"{base_url}/api/upload", files=files, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Daily data upload successful: {result['message']}")
            
            # Test prediction without features (time-series only)
            print("\n🔮 Testing Time-Series Prediction...")
            prediction_data = {
                "ds": "2024-01-01"
            }
            
            pred_response = requests.post(f"{base_url}/api/predict", 
                                        json=prediction_data, 
                                        headers={'Content-Type': 'application/json'},
                                        timeout=30)
            
            if pred_response.status_code == 200:
                pred_result = pred_response.json()
                print(f"✅ Time-series prediction successful: {pred_result['predicted_sales']:.2f} for {pred_result['prediction_date']}")
            else:
                print(f"❌ Time-series prediction failed: {pred_response.text}")
                
        else:
            print(f"❌ Daily data upload failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during daily data test: {e}")
        return False
    
    # Cleanup
    import os
    try:
        os.remove('test_monthly_data.csv')
        os.remove('test_daily_data.csv')
    except:
        pass
    
    return True

def test_web_interface():
    """Test the web interface accessibility"""
    
    base_url = "http://localhost:8000"
    
    print("\n🌐 Testing Web Interface...")
    try:
        # Test home page
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Home page accessible")
        else:
            print(f"❌ Home page error: {response.status_code}")
            
        # Test upload page
        response = requests.get(f"{base_url}/upload", timeout=10)
        if response.status_code == 200:
            print("✅ Upload page accessible")
        else:
            print(f"❌ Upload page error: {response.status_code}")
            
        # Test predict page
        response = requests.get(f"{base_url}/predict", timeout=10)
        if response.status_code in [200, 302]:  # 302 is redirect if no model
            print("✅ Predict page accessible")
        else:
            print(f"❌ Predict page error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error testing web interface: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Enhanced Flask Application Tests")
    print("=" * 60)
    print("📋 Prerequisites:")
    print("   1. Flask app should be running on localhost:8000")
    print("   2. Run: python sf_app.py")
    print("   3. Wait for 'Running on http://0.0.0.0:8000' message")
    print("=" * 60)
    
    # Test web interface first
    web_success = test_web_interface()
    
    if web_success:
        # Test API functionality
        api_success = test_api_upload_and_predict()
        
        if api_success:
            print("\n" + "=" * 60)
            print("✅ ALL ENHANCED FEATURES TESTED SUCCESSFULLY!")
            print("=" * 60)
            print("\n🎉 The enhanced sales forecasting application is working correctly:")
            print("   ✓ Web interface accessible")
            print("   ✓ API endpoints functional")
            print("   ✓ Frequency detection working")
            print("   ✓ Enhanced model training")
            print("   ✓ Prediction with and without features")
            print("\n🌐 You can now access the application at: http://localhost:8000")
        else:
            print("\n❌ API tests failed. Check Flask app logs for errors.")
    else:
        print("\n❌ Web interface tests failed. Make sure Flask app is running.")
    
    print("\n📝 Next steps:")
    print("   1. Open http://localhost:8000 in your browser")
    print("   2. Upload a CSV file with 'ds' and 'y' columns")
    print("   3. View the enhanced prediction results with visualization")
    print("   4. Test with both daily and monthly datasets")
