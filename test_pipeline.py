#!/usr/bin/env python3
"""
Test script to verify the sales forecasting pipeline works for both:
1. Featured datasets (with additional columns like MRP, Discount value)
2. Non-featured datasets (only 'ds' and 'y' columns)
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

from sales_forecast.pipeline.data_preparation import clean_data, data_identifying
from sales_forecast.pipeline.model_training import model_training
from sales_forecast.pipeline.forecast import predict_future

def test_featured_dataset():
    """Test with a dataset that has features"""
    print("=" * 60)
    print("TESTING FEATURED DATASET")
    print("=" * 60)
    
    try:
        # Load featured dataset
        data = pd.read_csv('PF0033301_data (1).csv')
        print(f"Loaded featured dataset with shape: {data.shape}")
        print(f"Columns: {list(data.columns)}")
        
        # Clean and identify data
        data, y = clean_data(data)
        date_column, feature_columns, target_column = data_identifying(data, y)
        
        print(f"Date column: {date_column}")
        print(f"Feature columns: {feature_columns}")
        print(f"Target column: {target_column}")
        
        # Prepare training and test data
        if feature_columns and len(feature_columns) > 0:
            columns_to_use = [date_column] + feature_columns + [target_column]
        else:
            columns_to_use = [date_column, target_column]
        
        train = data[columns_to_use][:-3]
        test = data[columns_to_use][-3:]
        
        print(f"Training data shape: {train.shape}")
        print(f"Test data shape: {test.shape}")
        
        # Train models
        print("\nTraining models...")
        model_results = model_training(train, test, data, date_column, target_column, feature_columns)
        best_model = model_results[0]
        best_model_name = model_results[7]
        
        print(f"Best model: {best_model_name}")
        
        # Test prediction with features
        prediction_data_with_features = pd.DataFrame({
            'ds': ['2024-04-01'],
            'MRP': [285.0],
            'Discount value': [500000.0]
        })
        prediction_data_with_features['ds'] = pd.to_datetime(prediction_data_with_features['ds'])
        
        print("\nTesting prediction with features...")
        prediction = predict_future(best_model, best_model_name, prediction_data_with_features)
        print(f"Prediction with features: {prediction}")
        
        # Test prediction without features (ds only)
        prediction_data_ds_only = pd.DataFrame({
            'ds': ['2024-04-01']
        })
        prediction_data_ds_only['ds'] = pd.to_datetime(prediction_data_ds_only['ds'])
        
        print("\nTesting prediction with ds only...")
        prediction_ds_only = predict_future(best_model, best_model_name, prediction_data_ds_only)
        print(f"Prediction with ds only: {prediction_ds_only}")
        
        print("✅ Featured dataset test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Featured dataset test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_non_featured_dataset():
    """Test with a dataset that has only ds and y columns"""
    print("\n" + "=" * 60)
    print("TESTING NON-FEATURED DATASET (DS + Y ONLY)")
    print("=" * 60)
    
    try:
        # Load non-featured dataset
        data = pd.read_csv('test_ds_y_only.csv')
        print(f"Loaded non-featured dataset with shape: {data.shape}")
        print(f"Columns: {list(data.columns)}")
        
        # Clean and identify data
        data, y = clean_data(data)
        date_column, feature_columns, target_column = data_identifying(data, y)
        
        print(f"Date column: {date_column}")
        print(f"Feature columns: {feature_columns}")
        print(f"Target column: {target_column}")
        
        # Prepare training and test data
        if feature_columns and len(feature_columns) > 0:
            columns_to_use = [date_column] + feature_columns + [target_column]
        else:
            columns_to_use = [date_column, target_column]
        
        train = data[columns_to_use][:-3]
        test = data[columns_to_use][-3:]
        
        print(f"Training data shape: {train.shape}")
        print(f"Test data shape: {test.shape}")
        
        # Train models
        print("\nTraining models...")
        model_results = model_training(train, test, data, date_column, target_column, feature_columns)
        best_model = model_results[0]
        best_model_name = model_results[7]
        
        print(f"Best model: {best_model_name}")
        
        # Test prediction with ds only
        prediction_data_ds_only = pd.DataFrame({
            'ds': ['2022-07-01']
        })
        prediction_data_ds_only['ds'] = pd.to_datetime(prediction_data_ds_only['ds'])
        
        print("\nTesting prediction with ds only...")
        prediction = predict_future(best_model, best_model_name, prediction_data_ds_only)
        print(f"Prediction with ds only: {prediction}")
        
        print("✅ Non-featured dataset test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Non-featured dataset test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("SALES FORECASTING PIPELINE TEST")
    print("Testing compatibility with featured and non-featured datasets")
    
    # Test featured dataset
    featured_test_passed = test_featured_dataset()
    
    # Test non-featured dataset
    non_featured_test_passed = test_non_featured_dataset()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Featured dataset test: {'✅ PASSED' if featured_test_passed else '❌ FAILED'}")
    print(f"Non-featured dataset test: {'✅ PASSED' if non_featured_test_passed else '❌ FAILED'}")
    
    if featured_test_passed and non_featured_test_passed:
        print("\n🎉 ALL TESTS PASSED! The pipeline works for both featured and non-featured datasets.")
    else:
        print("\n⚠️  Some tests failed. The pipeline may need adjustments.")

if __name__ == "__main__":
    main()
