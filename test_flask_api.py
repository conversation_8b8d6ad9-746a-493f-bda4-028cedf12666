#!/usr/bin/env python3
"""
Test script to verify the Flask API endpoints work for both:
1. Featured datasets (with additional columns like MRP, Discount value)
2. Non-featured datasets (only 'ds' and 'y' columns)
"""

import requests
import json
import time

def test_api_with_featured_dataset():
    """Test API with featured dataset"""
    print("=" * 60)
    print("TESTING API WITH FEATURED DATASET")
    print("=" * 60)
    
    try:
        # Upload featured dataset
        with open('PF0033301_data (1).csv', 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/api/upload', files=files)
        
        print(f"Upload response status: {response.status_code}")
        print(f"Upload response: {response.json()}")
        
        if response.status_code == 200:
            # Test prediction with features
            prediction_data = {
                'ds': '2024-04-01',
                'MRP': 285.0,
                'Discount value': 500000.0
            }
            
            response = requests.post('http://localhost:8000/api/predict', 
                                   json=prediction_data,
                                   headers={'Content-Type': 'application/json'})
            
            print(f"Prediction with features status: {response.status_code}")
            print(f"Prediction with features: {response.json()}")
            
            # Test prediction without features (ds only)
            prediction_data_ds_only = {
                'ds': '2024-04-01'
            }
            
            response = requests.post('http://localhost:8000/api/predict', 
                                   json=prediction_data_ds_only,
                                   headers={'Content-Type': 'application/json'})
            
            print(f"Prediction ds-only status: {response.status_code}")
            print(f"Prediction ds-only: {response.json()}")
            
            print("✅ Featured dataset API test PASSED")
            return True
        else:
            print("❌ Featured dataset upload failed")
            return False
            
    except Exception as e:
        print(f"❌ Featured dataset API test FAILED: {e}")
        return False

def test_api_with_non_featured_dataset():
    """Test API with non-featured dataset"""
    print("\n" + "=" * 60)
    print("TESTING API WITH NON-FEATURED DATASET")
    print("=" * 60)
    
    try:
        # Upload non-featured dataset
        with open('test_ds_y_only.csv', 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/api/upload', files=files)
        
        print(f"Upload response status: {response.status_code}")
        print(f"Upload response: {response.json()}")
        
        if response.status_code == 200:
            # Test prediction with ds only
            prediction_data = {
                'ds': '2022-07-01'
            }
            
            response = requests.post('http://localhost:8000/api/predict', 
                                   json=prediction_data,
                                   headers={'Content-Type': 'application/json'})
            
            print(f"Prediction ds-only status: {response.status_code}")
            print(f"Prediction ds-only: {response.json()}")
            
            print("✅ Non-featured dataset API test PASSED")
            return True
        else:
            print("❌ Non-featured dataset upload failed")
            return False
            
    except Exception as e:
        print(f"❌ Non-featured dataset API test FAILED: {e}")
        return False

def main():
    """Run API tests"""
    print("FLASK API TEST")
    print("Testing API endpoints with featured and non-featured datasets")
    print("Make sure the Flask app is running on http://localhost:8000")
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    # Test featured dataset
    featured_test_passed = test_api_with_featured_dataset()
    
    # Test non-featured dataset  
    non_featured_test_passed = test_api_with_non_featured_dataset()
    
    # Summary
    print("\n" + "=" * 60)
    print("API TEST SUMMARY")
    print("=" * 60)
    print(f"Featured dataset API test: {'✅ PASSED' if featured_test_passed else '❌ FAILED'}")
    print(f"Non-featured dataset API test: {'✅ PASSED' if non_featured_test_passed else '❌ FAILED'}")
    
    if featured_test_passed and non_featured_test_passed:
        print("\n🎉 ALL API TESTS PASSED! The Flask API works for both featured and non-featured datasets.")
    else:
        print("\n⚠️  Some API tests failed. Check the Flask application.")

if __name__ == "__main__":
    main()
