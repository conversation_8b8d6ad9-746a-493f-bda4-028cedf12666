<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sales Forecast{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
            background-color: #f8f9fa;
        }
        .navbar {
            margin-bottom: 2rem;
            background-color: #343a40;
        }
        .card {
            margin-bottom: 1.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .footer {
            margin-top: 2rem;
            padding: 1rem 0;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
        .alert {
            margin-top: 1rem;
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">Sales Forecast</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}"><i class="bi bi-house-door"></i> Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('upload_dataset') }}"><i class="bi bi-upload"></i> Upload Dataset</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('predict') }}"><i class="bi bi-graph-up"></i> Make Prediction</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('predictions_history') }}"><i class="bi bi-clock-history"></i> Predictions History</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('documentation') }}"><i class="bi bi-file-text"></i> Documentation</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != '_' else 'info' }} alert-dismissible fade show shadow-lg">
                        <div class="d-flex align-items-center">
                            {% if category == 'success' %}
                                <i class="bi bi-check-circle-fill me-2 fs-4"></i>
                            {% elif category == 'danger' %}
                                <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
                            {% elif category == 'warning' %}
                                <i class="bi bi-exclamation-circle-fill me-2 fs-4"></i>
                            {% else %}
                                <i class="bi bi-info-circle-fill me-2 fs-4"></i>
                            {% endif %}
                            <div>{{ message }}</div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer text-center">
        <div class="container">
            <span>Sales Forecast Application &copy; 2025</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
