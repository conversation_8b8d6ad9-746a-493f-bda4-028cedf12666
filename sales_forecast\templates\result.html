{% extends "base.html" %}

{% block title %}Sales Forecast - Prediction Result{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h2 class="mb-0">Prediction Result</h2>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h5>Model Used: {{ model_name }}</h5>
                    {% if data_frequency %}
                    <p><strong>Data Frequency:</strong> {{ data_frequency|title }}</p>
                    {% endif %}
                </div>

                <div class="card mb-4">
                    <div class="card-body text-center">
                        <h5 class="card-title">Sales Prediction Result</h5>
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-2 text-muted">Prediction Date</h6>
                                        <p class="card-text fs-4">{{ result.prediction_date }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-2 text-muted">Predicted Sales</h6>
                                        <p class="card-text prediction-value">{{ result.predicted_sales|round(2) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% if chart_data %}
                <!-- Forecast Visualization -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Sales Forecast Visualization</h5>
                    </div>
                    <div class="card-body">
                        <div style="height: 400px;">
                            <canvas id="forecastChart"></canvas>
                        </div>
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>Chart Legend:</strong><br>
                                        • Blue line: Historical sales data<br>
                                        • Colored points: Your calculated predictions<br>
                                        • Each prediction has a unique color<br>
                                        • Latest prediction is highlighted
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb"></i>
                                        <strong>Understanding the Chart:</strong><br>
                                        • Historical trend shows past sales patterns<br>
                                        • Only your calculated predictions are shown<br>
                                        • Multiple predictions accumulate over time<br>
                                        • <a href="{{ url_for('predictions_history') }}">View all predictions</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="d-flex justify-content-between">
                    <div>
                        <a href="{{ url_for('predict') }}" class="btn btn-primary">Make Another Prediction</a>
                        <a href="{{ url_for('predictions_history') }}" class="btn btn-info">View All Predictions</a>
                    </div>
                    <a href="{{ url_for('home') }}" class="btn btn-secondary">Back to Home</a>
                </div>
            </div>
        </div>
    </div>
</div>

{% if chart_data %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chartData = {{ chart_data|safe }};

    if (chartData) {
        const ctx = document.getElementById('forecastChart').getContext('2d');

        // Enhanced chart configuration for better confidence interval display
        chartData.options = {
            ...chartData.options,
            plugins: {
                ...chartData.options.plugins,
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return 'Date: ' + context[0].label;
                        },
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += new Intl.NumberFormat('en-US', {
                                    style: 'decimal',
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }).format(context.parsed.y);
                            }
                            return label;
                        },
                        afterBody: function(context) {
                            // Add confidence interval information
                            const dataIndex = context[0].dataIndex;
                            const datasets = context[0].chart.data.datasets;

                            let info = [];
                            datasets.forEach(dataset => {
                                if (dataset.label.includes('Confidence') && dataset.data[dataIndex] !== null) {
                                    const value = dataset.data[dataIndex];
                                    info.push(`${dataset.label}: ${value.toFixed(2)}`);
                                }
                            });

                            if (info.length > 0) {
                                info.unshift(''); // Add empty line
                                info.unshift('Confidence Interval:');
                            }

                            return info;
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        filter: function(item, chart) {
                            // Hide confidence bound labels from legend (they're explained in the description)
                            return !item.text.includes('Confidence Bound');
                        }
                    }
                }
            },
            scales: {
                ...chartData.options.scales,
                y: {
                    ...chartData.options.scales.y,
                    beginAtZero: false,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    ...chartData.options.scales.x,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        };

        new Chart(ctx, chartData);
    }
});
</script>
{% endif %}

{% endblock %}
