{% extends "base.html" %}

{% block title %}Sales Forecast - Documentation{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h2 class="mb-0">Documentation</h2>
            </div>
            <div class="card-body">
                <h3>Overview</h3>
                <p>
                    The Sales Forecast application helps you predict future sales based on historical data.
                    It uses machine learning algorithms to analyze patterns in your data and generate accurate forecasts.
                </p>
                
                <hr>
                
                <h3>Dataset Requirements</h3>
                <p>To use this application, your dataset should meet the following requirements:</p>
                <ul>
                    <li>CSV format</li>
                    <li>At least 10 rows of data</li>
                    <li>A date column (will be automatically detected)</li>
                    <li>Numeric feature columns</li>
                    <li>A target column representing sales values</li>
                </ul>
                
                <hr>
                
                <h3>Web Interface</h3>
                <h5>Upload Dataset</h5>
                <p>
                    Use the <a href="{{ url_for('upload_dataset') }}">Upload Dataset</a> page to upload your CSV file.
                    The application will automatically process your data, identify relevant columns, and train multiple models.
                    The best performing model will be selected for making predictions.
                </p>
                
                <h5>Make Prediction</h5>
                <p>
                    After training a model, use the <a href="{{ url_for('predict') }}">Make Prediction</a> page to generate forecasts.
                    Enter values for each required feature and submit the form to get a prediction.
                </p>
                
                <hr>
                
                <h3>API Documentation</h3>
                <p>The application also provides REST API endpoints for programmatic access:</p>
                
                <h5>Upload Dataset API</h5>
                <pre class="bg-light p-3">
POST /api/upload
Content-Type: multipart/form-data

Parameters:
- file: CSV file (required)

Response:
{
    "message": "Model trained successfully. Best model: [model_name]"
}
                </pre>
                
                <h5>Prediction API</h5>
                <pre class="bg-light p-3">
POST /api/predict
Content-Type: application/json

Request Body:
{
    "ds": "2023-01-01",
    "feature1": value1,
    "feature2": value2,
    ...
}

Response:
{
    "prediction_date": "2023-01-01",
    "predicted_sales": 1234.56
}
                </pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}
