import pandas as pd
import json
from datetime import datetime, timedelta
import numpy as np

def calculate_confidence_intervals(model, model_name, prediction_data, predictions, confidence_level=0.95):
    """
    Calculate confidence intervals for predictions based on model type

    Args:
        model: Trained model
        model_name: Name of the model
        prediction_data: Data used for predictions
        predictions: Point predictions
        confidence_level: Confidence level (default 0.95 for 95% CI)

    Returns:
        Dictionary with 'lower' and 'upper' confidence bounds
    """

    alpha = 1 - confidence_level
    z_score = 1.96  # For 95% confidence interval

    try:
        if model_name == 'Prophet':
            # Prophet provides built-in uncertainty estimation
            if hasattr(model, 'predict'):
                # Prepare future dataframe for Prophet
                future = prediction_data[['ds']].copy()
                future['ds'] = pd.to_datetime(future['ds'])

                # Get full forecast with uncertainty
                forecast = model.predict(future)

                if 'yhat_lower' in forecast.columns and 'yhat_upper' in forecast.columns:
                    return {
                        'lower': forecast['yhat_lower'].values,
                        'upper': forecast['yhat_upper'].values
                    }

        elif model_name == 'SARIMA':
            # SARIMA can provide confidence intervals
            if hasattr(model, 'get_forecast'):
                try:
                    forecast_result = model.get_forecast(steps=len(predictions))
                    conf_int = forecast_result.conf_int(alpha=alpha)

                    return {
                        'lower': conf_int.iloc[:, 0].values,
                        'upper': conf_int.iloc[:, 1].values
                    }
                except:
                    pass

        # For other models or fallback, estimate confidence intervals
        # using historical prediction errors or simple percentage bounds
        pred_array = np.array(predictions)

        # Simple approach: use ±15% of prediction as confidence bounds
        margin = pred_array * 0.15

        return {
            'lower': pred_array - margin,
            'upper': pred_array + margin
        }

    except Exception as e:
        print(f"Error calculating confidence intervals: {e}")

        # Fallback: simple percentage-based bounds
        pred_array = np.array(predictions)
        margin = pred_array * 0.15

        return {
            'lower': pred_array - margin,
            'upper': pred_array + margin
        }

# This function is now implemented as prepare_chart_data_original below

def prepare_user_predictions_chart_data(historical_data, user_predictions_list, date_column='ds',
                                       target_column='y', model_name='Unknown'):
    """
    Prepare data for Chart.js visualization showing historical data and only user-calculated predictions

    Args:
        historical_data: DataFrame with historical data
        user_predictions_list: List of dictionaries with user predictions
                              Each dict should have: {'date': date, 'prediction': value, 'timestamp': when_made}
        date_column: Name of the date column
        target_column: Name of the target column
        model_name: Name of the model used for predictions

    Returns:
        Dictionary with chart data ready for Chart.js
    """

    # Prepare historical data
    historical_data_sorted = historical_data.sort_values(date_column)

    # Convert dates to strings for JSON serialization
    historical_dates = historical_data_sorted[date_column].dt.strftime('%Y-%m-%d').tolist()
    historical_values = historical_data_sorted[target_column].tolist()

    # Prepare user predictions data
    prediction_dates = []
    prediction_values = []
    prediction_colors = []
    prediction_point_styles = []
    prediction_point_radii = []

    # Color palette for different predictions
    colors = [
        'rgb(255, 99, 132)',   # Red
        'rgb(54, 162, 235)',   # Blue
        'rgb(255, 205, 86)',   # Yellow
        'rgb(75, 192, 192)',   # Green
        'rgb(153, 102, 255)',  # Purple
        'rgb(255, 159, 64)',   # Orange
        'rgb(199, 199, 199)',  # Grey
        'rgb(83, 102, 147)'    # Dark Blue
    ]

    if user_predictions_list:
        for i, pred in enumerate(user_predictions_list):
            pred_date = pred['date']
            if isinstance(pred_date, str):
                pred_date_str = pred_date
            else:
                pred_date_str = pd.to_datetime(pred_date).strftime('%Y-%m-%d')

            prediction_dates.append(pred_date_str)
            prediction_values.append(pred['prediction'])

            # Use different colors for different predictions
            color_index = i % len(colors)
            prediction_colors.append(colors[color_index])
            prediction_point_styles.append('circle')
            prediction_point_radii.append(6)

    # Sort prediction dates to show proper time progression
    if user_predictions_list:
        # Sort predictions by date
        sorted_predictions = sorted(user_predictions_list, key=lambda x: x['date'])
        prediction_dates = [pred['date'] for pred in sorted_predictions]
        prediction_values = [pred['prediction'] for pred in sorted_predictions]

    # Create a complete timeline showing the gap between historical and predictions
    all_dates = historical_dates.copy()

    # Add gap indicators if there's a time gap between historical and predictions
    if prediction_dates:
        last_historical_date = pd.to_datetime(historical_dates[-1])
        first_prediction_date = pd.to_datetime(prediction_dates[0])

        # If there's a significant gap (more than expected frequency), add intermediate points
        gap_days = (first_prediction_date - last_historical_date).days
        if gap_days > 60:  # More than 2 months gap
            # Add a few intermediate dates to show the gap visually
            gap_months = min(3, gap_days // 30)  # Add up to 3 intermediate points
            for i in range(1, gap_months + 1):
                gap_date = last_historical_date + pd.DateOffset(months=i)
                all_dates.append(gap_date.strftime('%Y-%m-%d'))

    # Add prediction dates
    all_dates.extend(prediction_dates)

    # Create historical data array with proper None values for gaps and predictions
    historical_data_array = historical_values.copy()

    # Add None values for gap periods
    gap_count = len(all_dates) - len(historical_dates) - len(prediction_dates)
    historical_data_array.extend([None] * (gap_count + len(prediction_dates)))

    # Create point styles for historical data
    historical_point_radii = [3] * len(historical_values) + [0] * (len(all_dates) - len(historical_values))
    historical_point_colors = ['rgb(54, 162, 235)'] * len(historical_values) + ['transparent'] * (len(all_dates) - len(historical_values))

    # Create datasets for Chart.js
    datasets = [
        {
            'label': 'Historical Sales',
            'data': historical_data_array,
            'borderColor': 'rgb(54, 162, 235)',
            'backgroundColor': 'rgba(54, 162, 235, 0.1)',
            'fill': False,
            'tension': 0.1,
            'pointRadius': historical_point_radii,
            'pointHoverRadius': [r + 2 if r > 0 else 0 for r in historical_point_radii],
            'pointStyle': ['circle'] * len(all_dates),
            'pointBackgroundColor': historical_point_colors,
            'pointBorderColor': historical_point_colors
        }
    ]

    # Add individual prediction points as separate datasets for better control
    if user_predictions_list:
        sorted_predictions = sorted(user_predictions_list, key=lambda x: x['date'])

        for i, pred in enumerate(sorted_predictions):
            pred_date_str = pred['date'] if isinstance(pred['date'], str) else pd.to_datetime(pred['date']).strftime('%Y-%m-%d')

            # Find the index of this prediction date in all_dates
            try:
                pred_date_index = all_dates.index(pred_date_str)
            except ValueError:
                # If date not found, skip this prediction
                continue

            # Create data array with None values except for this prediction
            pred_data = [None] * len(all_dates)
            pred_data[pred_date_index] = pred['prediction']

            color_index = i % len(colors)
            datasets.append({
                'label': f'Prediction {i+1} ({pred_date_str}): {int(pred["prediction"])}',
                'data': pred_data,
                'borderColor': colors[color_index],
                'backgroundColor': colors[color_index],
                'fill': False,
                'tension': 0,
                'pointRadius': [0] * len(all_dates),
                'pointHoverRadius': [0] * len(all_dates),
                'pointStyle': ['circle'] * len(all_dates),
                'pointBackgroundColor': ['transparent'] * len(all_dates),
                'pointBorderColor': ['transparent'] * len(all_dates),
                'showLine': False  # Only show points, no connecting lines
            })

            # Set the specific point properties
            datasets[-1]['pointRadius'][pred_date_index] = 8
            datasets[-1]['pointHoverRadius'][pred_date_index] = 10
            datasets[-1]['pointBackgroundColor'][pred_date_index] = colors[color_index]
            datasets[-1]['pointBorderColor'][pred_date_index] = colors[color_index]
            datasets[-1]['pointStyle'][pred_date_index] = 'circle'

    chart_data = {
        'labels': all_dates,
        'datasets': datasets
    }

    # Chart configuration
    chart_config = {
        'type': 'line',
        'data': chart_data,
        'options': {
            'responsive': True,
            'maintainAspectRatio': False,
            'plugins': {
                'title': {
                    'display': True,
                    'text': f'Sales History & Your Predictions - {model_name}',
                    'font': {
                        'size': 16
                    }
                },
                'legend': {
                    'display': True,
                    'position': 'top'
                }
            },
            'scales': {
                'x': {
                    'display': True,
                    'title': {
                        'display': True,
                        'text': 'Date'
                    },
                    'type': 'category'
                },
                'y': {
                    'display': True,
                    'title': {
                        'display': True,
                        'text': 'Sales Value'
                    }
                }
            },
            'interaction': {
                'intersect': False,
                'mode': 'index'
            }
        }
    }

    return chart_config

def prepare_chart_data_original(historical_data, predictions, date_column='ds', target_column='y',
                      prediction_dates=None, model_name='Unknown', confidence_intervals=None,
                      highlight_date=None):
    """
    Original prepare_chart_data function for backward compatibility
    """
    # Prepare historical data
    historical_data_sorted = historical_data.sort_values(date_column)

    # Convert dates to strings for JSON serialization
    historical_dates = historical_data_sorted[date_column].dt.strftime('%Y-%m-%d').tolist()
    historical_values = historical_data_sorted[target_column].tolist()

    # Prepare prediction data
    if prediction_dates is None:
        # Generate future dates based on the last historical date
        last_date = historical_data_sorted[date_column].max()
        prediction_dates = []
        for i in range(len(predictions)):
            if 'daily' in str(historical_data_sorted[date_column].diff().mode().iloc[0]).lower():
                next_date = last_date + timedelta(days=i+1)
            elif 'weekly' in str(historical_data_sorted[date_column].diff().mode().iloc[0]).lower():
                next_date = last_date + timedelta(weeks=i+1)
            else:  # monthly or other
                # Add months (approximate)
                next_date = last_date + timedelta(days=30*(i+1))
            prediction_dates.append(next_date)

    # Convert prediction dates to strings
    if isinstance(prediction_dates[0], str):
        pred_dates_str = prediction_dates
    else:
        pred_dates_str = [pd.to_datetime(date).strftime('%Y-%m-%d') for date in prediction_dates]

    # Convert predictions to list if it's not already
    if hasattr(predictions, 'tolist'):
        pred_values = predictions.tolist()
    elif hasattr(predictions, '__iter__'):
        pred_values = list(predictions)
    else:
        pred_values = [predictions]

    # Combine all dates for x-axis
    all_dates = historical_dates + pred_dates_str

    # Create point styles for highlighting
    historical_point_styles = []
    prediction_point_styles = []
    historical_point_radii = []
    prediction_point_radii = []
    historical_point_colors = []
    prediction_point_colors = []

    # Set up highlighting for historical data
    for date in historical_dates:
        if highlight_date and date == highlight_date:
            historical_point_styles.append('star')
            historical_point_radii.append(8)
            historical_point_colors.append('rgb(255, 193, 7)')  # Yellow for highlight
        else:
            historical_point_styles.append('circle')
            historical_point_radii.append(3)
            historical_point_colors.append('rgb(54, 162, 235)')

    # Set up highlighting for prediction data
    for date in pred_dates_str:
        if highlight_date and date == highlight_date:
            prediction_point_styles.append('star')
            prediction_point_radii.append(10)
            prediction_point_colors.append('rgb(255, 193, 7)')  # Yellow for highlight
        else:
            prediction_point_styles.append('circle')
            prediction_point_radii.append(4)
            prediction_point_colors.append('rgb(255, 99, 132)')

    # Create datasets for Chart.js
    datasets = [
        {
            'label': 'Historical Sales',
            'data': historical_values + [None] * len(pred_values),
            'borderColor': 'rgb(54, 162, 235)',
            'backgroundColor': 'rgba(54, 162, 235, 0.1)',
            'fill': False,
            'tension': 0.1,
            'pointRadius': historical_point_radii + [0] * len(pred_values),
            'pointHoverRadius': [r + 2 for r in historical_point_radii] + [0] * len(pred_values),
            'pointStyle': historical_point_styles + ['circle'] * len(pred_values),
            'pointBackgroundColor': historical_point_colors + ['transparent'] * len(pred_values),
            'pointBorderColor': historical_point_colors + ['transparent'] * len(pred_values)
        },
        {
            'label': f'Predicted Sales ({model_name})',
            'data': [None] * len(historical_values) + pred_values,
            'borderColor': 'rgb(255, 99, 132)',
            'backgroundColor': 'rgba(255, 99, 132, 0.1)',
            'fill': False,
            'tension': 0.1,
            'pointRadius': [0] * len(historical_values) + prediction_point_radii,
            'pointHoverRadius': [0] * len(historical_values) + [r + 2 for r in prediction_point_radii],
            'borderDash': [5, 5],
            'pointStyle': ['circle'] * len(historical_values) + prediction_point_styles,
            'pointBackgroundColor': ['transparent'] * len(historical_values) + prediction_point_colors,
            'pointBorderColor': ['transparent'] * len(historical_values) + prediction_point_colors
        }
    ]

    # Add confidence intervals if provided
    if confidence_intervals and 'lower' in confidence_intervals and 'upper' in confidence_intervals:
        lower_bounds = confidence_intervals['lower']
        upper_bounds = confidence_intervals['upper']

        # Convert to lists if needed
        if hasattr(lower_bounds, 'tolist'):
            lower_bounds = lower_bounds.tolist()
        if hasattr(upper_bounds, 'tolist'):
            upper_bounds = upper_bounds.tolist()

        # Add confidence interval datasets
        datasets.extend([
            {
                'label': 'Upper Confidence Bound',
                'data': [None] * len(historical_values) + upper_bounds,
                'borderColor': 'rgba(255, 99, 132, 0.3)',
                'backgroundColor': 'rgba(255, 99, 132, 0.1)',
                'fill': '+1',  # Fill to next dataset
                'tension': 0.1,
                'pointRadius': 0,
                'pointHoverRadius': 0,
                'borderDash': [2, 2],
                'order': 2
            },
            {
                'label': 'Lower Confidence Bound',
                'data': [None] * len(historical_values) + lower_bounds,
                'borderColor': 'rgba(255, 99, 132, 0.3)',
                'backgroundColor': 'rgba(255, 99, 132, 0.1)',
                'fill': False,
                'tension': 0.1,
                'pointRadius': 0,
                'pointHoverRadius': 0,
                'borderDash': [2, 2],
                'order': 3
            }
        ])

    chart_data = {
        'labels': all_dates,
        'datasets': datasets
    }

    # Chart configuration
    chart_config = {
        'type': 'line',
        'data': chart_data,
        'options': {
            'responsive': True,
            'maintainAspectRatio': False,
            'plugins': {
                'title': {
                    'display': True,
                    'text': f'Sales Forecast - {model_name}',
                    'font': {
                        'size': 16
                    }
                },
                'legend': {
                    'display': True,
                    'position': 'top'
                }
            },
            'scales': {
                'x': {
                    'display': True,
                    'title': {
                        'display': True,
                        'text': 'Date'
                    },
                    'type': 'category'
                },
                'y': {
                    'display': True,
                    'title': {
                        'display': True,
                        'text': 'Sales Value'
                    }
                }
            },
            'interaction': {
                'intersect': False,
                'mode': 'index'
            }
        }
    }

    return chart_config

# Alias for backward compatibility
prepare_chart_data = prepare_chart_data_original

def store_user_prediction(prediction_date, prediction_value, model_name, storage_file='user_predictions.json'):
    """
    Store a user prediction to a JSON file for later visualization

    Args:
        prediction_date: Date of the prediction
        prediction_value: Predicted value
        model_name: Name of the model used
        storage_file: Path to the storage file
    """
    import os
    from datetime import datetime

    # Create prediction record with rounded whole number
    prediction_record = {
        'date': prediction_date if isinstance(prediction_date, str) else prediction_date.strftime('%Y-%m-%d'),
        'prediction': int(round(float(prediction_value))),
        'model_name': model_name,
        'timestamp': datetime.now().isoformat()
    }

    # Load existing predictions
    predictions = []
    if os.path.exists(storage_file):
        try:
            with open(storage_file, 'r') as f:
                predictions = json.load(f)
        except:
            predictions = []

    # Add new prediction
    predictions.append(prediction_record)

    # Keep only the last 50 predictions to avoid file getting too large
    predictions = predictions[-50:]

    # Save back to file
    with open(storage_file, 'w') as f:
        json.dump(predictions, f, indent=2)

    return prediction_record

def get_user_predictions(model_name=None, storage_file='user_predictions.json'):
    """
    Retrieve user predictions from storage

    Args:
        model_name: Filter by model name (optional)
        storage_file: Path to the storage file

    Returns:
        List of prediction records
    """
    import os

    if not os.path.exists(storage_file):
        return []

    try:
        with open(storage_file, 'r') as f:
            predictions = json.load(f)

        # Filter by model name if specified
        if model_name:
            predictions = [p for p in predictions if p.get('model_name') == model_name]

        return predictions
    except:
        return []

def clear_user_predictions(storage_file='user_predictions.json'):
    """
    Clear all stored user predictions
    """
    import os

    if os.path.exists(storage_file):
        os.remove(storage_file)

def validate_prediction_value(prediction_value, model_name, prediction_date):
    """
    Validate prediction value and provide warnings for unusual values

    Args:
        prediction_value: The predicted value
        model_name: Name of the model used
        prediction_date: Date of the prediction

    Returns:
        dict with validation results and warnings
    """
    warnings = []
    is_valid = True
    adjusted_value = prediction_value

    # Check for negative values
    if prediction_value < 0:
        warnings.append({
            'type': 'negative_value',
            'message': f'Negative prediction ({prediction_value:.2f}) detected',
            'suggestion': 'This might indicate model issues or extreme input values'
        })
        # Optionally adjust to 0 (uncomment if desired)
        # adjusted_value = max(0, prediction_value)
        # warnings[-1]['adjustment'] = f'Adjusted to {adjusted_value}'

    # Check for extremely large values (potential outlier)
    if prediction_value > 1000000:  # Adjust threshold as needed
        warnings.append({
            'type': 'large_value',
            'message': f'Very large prediction ({prediction_value:.2f}) detected',
            'suggestion': 'Please verify input values are reasonable'
        })

    # Check for zero or very small values
    if 0 <= prediction_value < 0.01:
        warnings.append({
            'type': 'small_value',
            'message': f'Very small prediction ({prediction_value:.6f}) detected',
            'suggestion': 'This might indicate insufficient training data or model issues'
        })

    return {
        'is_valid': is_valid,
        'original_value': prediction_value,
        'adjusted_value': adjusted_value,
        'warnings': warnings,
        'model_name': model_name,
        'prediction_date': prediction_date
    }

def generate_forecast_chart_data(model, model_name, historical_data, forecast_periods=12,
                                date_column='ds', target_column='y', feature_columns=None,
                                highlight_date=None):
    """
    Generate forecast data for visualization with confidence intervals

    Args:
        model: Trained model
        model_name: Name of the model
        historical_data: Historical data DataFrame
        forecast_periods: Number of periods to forecast
        date_column: Name of the date column
        target_column: Name of the target column
        feature_columns: List of feature columns
        highlight_date: Specific date to highlight in the chart

    Returns:
        Dictionary with chart configuration including confidence intervals
    """
    from .forecast import predict_future

    # Get the last date from historical data
    last_date = historical_data[date_column].max()

    # Detect frequency from historical data
    date_diffs = historical_data[date_column].diff().dropna()
    avg_diff = date_diffs.mode().iloc[0] if len(date_diffs) > 0 else timedelta(days=30)

    # Generate future dates
    future_dates = []
    for i in range(1, forecast_periods + 1):
        if avg_diff <= timedelta(days=1):
            next_date = last_date + timedelta(days=i)
        elif avg_diff <= timedelta(days=7):
            next_date = last_date + timedelta(weeks=i)
        else:  # monthly or other
            # Add months (approximate)
            next_date = last_date + timedelta(days=30*i)
        future_dates.append(next_date)

    # Create prediction DataFrame
    prediction_data = pd.DataFrame({date_column: future_dates})

    # Add feature columns if they exist (use mean values as default)
    if feature_columns:
        for feature in feature_columns:
            if feature in historical_data.columns:
                # Use the mean of the last few values as default
                recent_values = historical_data[feature].tail(3)
                default_value = recent_values.mean() if len(recent_values) > 0 else 0
                prediction_data[feature] = default_value

    # Make predictions
    try:
        predictions = predict_future(model, model_name, prediction_data)

        # Calculate confidence intervals
        confidence_intervals = calculate_confidence_intervals(
            model=model,
            model_name=model_name,
            prediction_data=prediction_data,
            predictions=predictions
        )

        # Prepare chart data with confidence intervals and highlighting
        chart_config = prepare_chart_data(
            historical_data=historical_data,
            predictions=predictions,
            date_column=date_column,
            target_column=target_column,
            prediction_dates=future_dates,
            model_name=model_name,
            confidence_intervals=confidence_intervals,
            highlight_date=highlight_date
        )

        return chart_config

    except Exception as e:
        print(f"Error generating forecast chart: {e}")
        return None

def create_summary_stats(historical_data, predictions, target_column='y'):
    """
    Create summary statistics for the forecast

    Args:
        historical_data: Historical data DataFrame
        predictions: Array of prediction values
        target_column: Name of the target column

    Returns:
        Dictionary with summary statistics
    """

    # Historical statistics
    hist_mean = historical_data[target_column].mean()
    hist_std = historical_data[target_column].std()
    hist_min = historical_data[target_column].min()
    hist_max = historical_data[target_column].max()

    # Prediction statistics
    pred_array = np.array(predictions) if not isinstance(predictions, np.ndarray) else predictions
    pred_mean = np.mean(pred_array)
    pred_min = np.min(pred_array)
    pred_max = np.max(pred_array)

    # Growth rate (comparing last historical value to first prediction)
    last_historical = historical_data[target_column].iloc[-1]
    first_prediction = pred_array[0] if len(pred_array) > 0 else last_historical
    growth_rate = ((first_prediction - last_historical) / last_historical * 100) if last_historical != 0 else 0

    summary = {
        'historical': {
            'mean': round(hist_mean, 2),
            'std': round(hist_std, 2),
            'min': round(hist_min, 2),
            'max': round(hist_max, 2),
            'count': len(historical_data)
        },
        'predictions': {
            'mean': round(pred_mean, 2),
            'min': round(pred_min, 2),
            'max': round(pred_max, 2),
            'count': len(pred_array)
        },
        'growth_rate': round(growth_rate, 2)
    }

    return summary
