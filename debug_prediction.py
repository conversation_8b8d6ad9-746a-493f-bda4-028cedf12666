#!/usr/bin/env python3
"""
Debug script to test prediction functionality and identify the error
"""

import pandas as pd
import joblib
import json
import sys
import traceback

# Add current directory to path
sys.path.append('.')

from sales_forecast.pipeline.forecast import predict_future

def test_prediction_debug():
    """Test prediction with current model to identify the error"""
    print("=" * 60)
    print("DEBUGGING PREDICTION ERROR")
    print("=" * 60)
    
    try:
        # Load the current model and config
        print("Loading model and configuration...")
        
        # Load model
        best_model = joblib.load('models/best_model.pkl')
        print(f"Model loaded: {type(best_model)}")
        
        # Load model name
        with open('models/best_model_name.json', 'r') as f:
            model_config = json.load(f)
            best_model_name = model_config['best_model_name']
        print(f"Model name: {best_model_name}")
        
        # Load feature config
        with open('models/config.json', 'r') as f:
            config = json.load(f)
            feature_columns = config['feature_columns']
            target_column = config['target_column']
            date_column = config['date_column']
        
        print(f"Feature columns: {feature_columns}")
        print(f"Target column: {target_column}")
        print(f"Date column: {date_column}")
        
        # Test prediction with ds only (current model has no features)
        print("\nTesting prediction with ds only...")
        prediction_data = pd.DataFrame({
            'ds': ['2024-04-01']
        })
        prediction_data['ds'] = pd.to_datetime(prediction_data['ds'])
        
        print(f"Prediction data: {prediction_data}")
        print(f"Prediction data dtypes: {prediction_data.dtypes}")
        
        # Make prediction
        print("\nCalling predict_future...")
        prediction = predict_future(best_model, best_model_name, prediction_data)
        
        print(f"Prediction result: {prediction}")
        print(f"Prediction type: {type(prediction)}")
        
        if hasattr(prediction, '__len__'):
            print(f"Prediction length: {len(prediction)}")
            if len(prediction) > 0:
                print(f"First prediction value: {prediction[0]}")
                print(f"First prediction type: {type(prediction[0])}")
        
        # Test conversion to float
        try:
            float_prediction = float(prediction[0])
            print(f"Float conversion successful: {float_prediction}")
        except Exception as e:
            print(f"Float conversion failed: {e}")
            print(f"Prediction[0] value: {prediction[0]}")
            print(f"Prediction[0] type: {type(prediction[0])}")
        
        print("✅ Prediction test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Prediction test FAILED: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

def test_form_data_processing():
    """Test form data processing similar to Flask route"""
    print("\n" + "=" * 60)
    print("TESTING FORM DATA PROCESSING")
    print("=" * 60)
    
    try:
        # Simulate form data (what Flask receives)
        form_data = {'ds': '2024-04-01'}
        print(f"Simulated form data: {form_data}")
        
        # Convert to DataFrame (like in Flask route)
        df = pd.DataFrame([form_data])
        print(f"DataFrame: {df}")
        
        # Convert date column
        df['ds'] = pd.to_datetime(df['ds'])
        print(f"DataFrame after date conversion: {df}")
        print(f"DataFrame dtypes: {df.dtypes}")
        
        # Prepare prediction data (like in Flask route)
        prediction_data = df[['ds']].copy()
        print(f"Prediction data: {prediction_data}")
        
        print("✅ Form data processing test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Form data processing test FAILED: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

def main():
    """Run all debug tests"""
    print("PREDICTION ERROR DEBUG SCRIPT")
    
    # Test prediction functionality
    prediction_test_passed = test_prediction_debug()
    
    # Test form data processing
    form_test_passed = test_form_data_processing()
    
    # Summary
    print("\n" + "=" * 60)
    print("DEBUG TEST SUMMARY")
    print("=" * 60)
    print(f"Prediction test: {'✅ PASSED' if prediction_test_passed else '❌ FAILED'}")
    print(f"Form processing test: {'✅ PASSED' if form_test_passed else '❌ FAILED'}")
    
    if prediction_test_passed and form_test_passed:
        print("\n🎉 All debug tests passed! The issue might be elsewhere.")
    else:
        print("\n⚠️  Some debug tests failed. This helps identify the issue.")

if __name__ == "__main__":
    main()
